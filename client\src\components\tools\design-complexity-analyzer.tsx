import { useState, useRef, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  ArrowRight,
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  Info,
  Lightbulb,
  AlertTriangle,
  BarChart3,
  Zap,
  Layout,
  Type,
  ImageIcon,
  Layers,
  MessageSquare,
  Minus,
  Save,
  History,
  Heart,
  Trash2,
  Star,
  Edit3,
  RefreshCw,
  Calendar,
  Eye,
  Check,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useDesignAnalysis } from "@/hooks/useDesignAnalysis";
import { designAnalysisService } from "@/services/designAnalysisService";
import type { DesignAnalysis } from "@/lib/supabase";
import { supabase } from "@/lib/supabase";
import AnalysisCard from "./AnalysisCard";

// Make services available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).designAnalysisService = designAnalysisService;
  (window as any).supabase = supabase;
}

// Importar la imagen del personaje AI
import aiCharacter from "../../assets/ai-analyzer-character.png";

// Definir la interfaz para los resultados del análisis
interface AnalysisResult {
  score: number;
  complexity: {
    overall: number;
    color: number;
    layout: number;
    typography: number;
    elements: number;
  };
  areas: {
    name: string;
    score: number;
    icon: JSX.Element;
    description: string;
    recommendations: string[];
  }[];
  recommendations: {
    category: string;
    issue: string;
    importance: "alta" | "media" | "baja";
    recommendation: string;
  }[];
}

// Interfaz para los mensajes del agente
interface AgentMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
}

// Mensajes predefinidos del agente
const agentIntroMessages = [
  "¡Hola! Soy VisuAI, tu asistente de análisis visual. Puedo ayudarte a evaluar la complejidad y efectividad de tus diseños.",
  "Para comenzar, sube una imagen de tu diseño y haré un análisis completo de su complejidad visual.",
  "Te proporcionaré puntuaciones, métricas y recomendaciones prácticas para mejorar tu diseño.",
];

// Mensajes para las diferentes etapas del análisis
const agentAnalysisMessages = {
  start: [
    "Estoy analizando tu diseño con mi algoritmo de IA. Esto tomará solo unos segundos...",
    "Estoy identificando elementos visuales, evaluando colores, layout y tipografía...",
  ],
  poor: [
    "He identificado varias áreas que necesitan mejoras en tu diseño. La complejidad visual es alta, lo que puede afectar la experiencia del usuario.",
    "Te recomendaría revisar especialmente el uso del color y la distribución de elementos.",
  ],
  average: [
    "Tu diseño tiene un balance aceptable, pero hay aspectos específicos que podrían mejorar significativamente su efectividad.",
    "Con algunos ajustes estratégicos, podrías conseguir un diseño mucho más impactante y efectivo.",
  ],
  good: [
    "¡Buen trabajo! Tu diseño muestra un balance visual efectivo y una complejidad bien manejada.",
    "He identificado algunos pequeños ajustes que podrían llevarlo al siguiente nivel.",
  ],
  excellent: [
    "¡Excelente trabajo! Tu diseño muestra un equilibrio excepcional entre simplicidad y comunicación efectiva.",
    "Es un diseño muy efectivo con una complejidad visual óptima para su propósito.",
  ],
};

export default function DesignComplexityAnalyzer() {
  // Component is rendering successfully


  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(
    null,
  );
  const [progress, setProgress] = useState(0);
  const [agentMessages, setAgentMessages] = useState<AgentMessage[]>([]);
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [showAgent, setShowAgent] = useState(true);
  const [isAgentMinimized, setIsAgentMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState("analyze");
  const [currentAnalysisId, setCurrentAnalysisId] = useState<string | null>(null);
  const [isLoadedFromSaved, setIsLoadedFromSaved] = useState(false); // Track if current analysis was loaded from saved
  const [originalImageHash, setOriginalImageHash] = useState<string | null>(null); // Track original image to detect changes
  const [isUploadingImage, setIsUploadingImage] = useState(false); // Track image upload progress
  const messageEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados para la gestión de análisis
  const [selectedAnalysis, setSelectedAnalysis] = useState<DesignAnalysis | null>(null);
  const [isRenaming, setIsRenaming] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");

  // Hooks
  const { toast } = useToast();
  const { user, isLoading: authLoading } = useAuth();

  // We'll define isAuthenticated after getting hookIsAuthenticated from the hook

  // Use the design analysis hook (with error handling)
  let hookData;
  try {
    hookData = useDesignAnalysis();
  } catch (error) {
    // Provide fallback values
    hookData = {
      analyses: [],
      isLoadingAnalyses: false,
      saveAnalysis: async () => {},
      isSaving: false,
      toggleFavorite: async () => {},
      isTogglingFavorite: false,
      deleteAnalysis: async () => {},
      isDeleting: false,
      getFavoriteAnalyses: () => [],
      getRecentAnalyses: () => [],
      userStats: null,
      refetchAnalyses: async () => {},
      isAuthenticated: false
    };
  }

  const {
    analyses,
    isLoadingAnalyses: hookIsLoadingAnalyses,
    saveAnalysis,
    isSaving,
    toggleFavorite,
    isTogglingFavorite,
    deleteAnalysis,
    isDeleting,
    getFavoriteAnalyses,
    getRecentAnalyses,
    userStats,
    refetchAnalyses,
    isAuthenticated: hookIsAuthenticated
  } = hookData;

  // Use authentication status from hook, with fallback to local check
  const isAuthenticated = hookIsAuthenticated || (!!user && user.id !== undefined && user.id !== 'anonymous');

  // Debug logging for authentication state
  useEffect(() => {
    console.log('🔐 Visual Complexity Analyzer - Auth State:', {
      user,
      isAuthenticated,
      hookIsAuthenticated,
      authLoading,
      userId: user?.id,
      userEmail: user?.email,
      userName: user?.username
    });
  }, [user, isAuthenticated, hookIsAuthenticated, authLoading]);

  // Get data from the hook after it's been initialized (with safety checks)
  // Make these reactive to changes in the analyses array
  const recentAnalyses = useMemo(() => {
    return getRecentAnalyses ? getRecentAnalyses() : [];
  }, [getRecentAnalyses, analyses]); // Depend on both the function and the analyses array

  const favoriteAnalyses = useMemo(() => {
    return getFavoriteAnalyses ? getFavoriteAnalyses() : [];
  }, [getFavoriteAnalyses, analyses]); // Depend on both the function and the analyses array

  // Cargar análisis guardados cuando el usuario esté autenticado
  useEffect(() => {
    if (user && user.id !== "anonymous") {
      loadSavedAnalyses();
    }
  }, [user]); // Only depend on user changes

  const loadSavedAnalyses = async () => {
    if (!user || user.id === "anonymous") {
      return;
    }

    try {
      // Trigger a refetch of the analyses data if available
      if (refetchAnalyses) {
        await refetchAnalyses();
      }
    } catch (error) {
      console.error("Error loading saved analyses:", error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los análisis guardados",
        variant: "destructive",
      });
    }
  };

  const handleToggleFavorite = async (analysisId: string) => {
    console.log('❤️ Toggle Favorite clicked:', { analysisId, isAuthenticated, user });

    if (!isAuthenticated) {
      toast({
        title: "Autenticación requerida",
        description: "Debes iniciar sesión para marcar favoritos",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('❤️ Calling toggleFavorite function...');
      // Use the hook's toggleFavorite function which expects just the ID string
      await toggleFavorite(analysisId);

      console.log('❤️ Toggle favorite successful, data will be automatically refreshed by React Query');
      // No need to manually reload - React Query will invalidate and refetch automatically

      // The success toast is already handled by the hook's onSuccess callback
    } catch (error) {
      console.error("❤️ Error toggling favorite:", error);
      // Error toast is already handled by the hook's onError callback
    }
  };

  const handleRenameAnalysis = async (analysisId: string, newName: string) => {
    if (!user || user.id === "anonymous") return;

    try {
      await designAnalysisService.renameAnalysis(analysisId, newName);
      await loadSavedAnalyses(); // Reload to update the lists
      setIsRenaming(null);
      setRenameValue("");

      toast({
        title: "Renombrado",
        description: "Análisis renombrado correctamente",
      });
    } catch (error) {
      console.error("Error renaming analysis:", error);
      toast({
        title: "Error",
        description: "No se pudo renombrar el análisis",
        variant: "destructive",
      });
    }
  };

  const handleDeleteAnalysis = async (analysisId: string) => {
    if (!user || user.id === "anonymous") return;

    try {
      await designAnalysisService.deleteAnalysis(analysisId);
      await loadSavedAnalyses(); // Reload to update the lists

      toast({
        title: "Eliminado",
        description: "Análisis eliminado correctamente",
      });
    } catch (error) {
      console.error("Error deleting analysis:", error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el análisis",
        variant: "destructive",
      });
    }
  };

  const handleLoadAnalysis = async (analysis: DesignAnalysis) => {
    try {
      console.log('🔄 Loading saved analysis:', {
        analysisId: analysis.id,
        filename: analysis.original_filename,
        file_url: analysis.file_url
      });

      // CRITICAL FIX: Clear any existing preview URL from current session first
      // This prevents showing the last analyzed image instead of the specific saved analysis image
      setPreviewUrl(null);
      setSelectedFile(null);

      // Convert DesignAnalysis to AnalysisResult format
      const convertedResults: AnalysisResult = {
        score: analysis.overall_score,
        complexity: {
          overall: analysis.overall_score,
          color: analysis.complexity_scores.color || 0,
          layout: analysis.complexity_scores.layout || 0,
          typography: analysis.complexity_scores.typography || 0,
          elements: analysis.complexity_scores.elements || 0,
        },
        areas: analysis.analysis_areas.map(area => ({
          ...area,
          icon: <Layout className="h-5 w-5 text-blue-500" /> // Default icon
        })),
        recommendations: Array.isArray(analysis.recommendations)
          ? analysis.recommendations.map(rec =>
              typeof rec === 'string'
                ? { category: 'General', issue: rec, importance: 'media' as const, recommendation: rec }
                : rec
            )
          : []
      };

      // Load the results
      setAnalysisResults(convertedResults);
      setAnalysisComplete(true);
      setCurrentAnalysisId(analysis.id);
      setIsLoadedFromSaved(true); // Mark as loaded from saved analysis

      // Create a hash for the original image to track changes
      // Use file metadata from the analysis record to create a unique identifier
      const imageHash = `${analysis.original_filename}_${analysis.file_size}_${analysis.created_at}`;
      setOriginalImageHash(imageHash);

      // Load the original image if available
      console.log('🖼️ Loading specific image for analysis ID:', analysis.id, {
        file_url: analysis.file_url,
        filename: analysis.original_filename,
        file_type: analysis.file_type,
        hasFileUrl: !!analysis.file_url,
        fileUrlType: typeof analysis.file_url
      });

      if (analysis.file_url) {
        try {
          // Set loading state
          setIsLoadingImage(true);

          // ENHANCED: Comprehensive image loading with multiple fallbacks and debugging
          let imageUrl = analysis.file_url;

          // If it's not already a full URL, get the URL from Supabase using enhanced method
          if (!analysis.file_url.startsWith('http')) {
            console.log('🔍 Getting image URL for file path:', analysis.file_url);
            console.log('📊 Testing enhanced image loading approach...');

            const downloadUrl = await designAnalysisService.getImageUrl(analysis.file_url);
            if (downloadUrl) {
              imageUrl = downloadUrl;
              console.log('✅ Successfully obtained image URL:', {
                type: downloadUrl.startsWith('blob:') ? 'Object URL' : downloadUrl.startsWith('http') ? 'HTTP URL' : 'Unknown',
                url: downloadUrl.substring(0, 50) + '...'
              });
            } else {
              console.error('❌ Failed to obtain image URL from service - file may not exist');
              throw new Error('Image file not found in storage');
            }
          } else {
            // For HTTP URLs, try to convert to file path and re-download
            console.log('🔗 Converting HTTP URL to file path for re-download');
            try {
              const url = new URL(analysis.file_url);
              const pathSegments = url.pathname.split('/').filter(segment => segment);
              if (pathSegments.length >= 2) {
                const filePath = pathSegments.slice(-2).join('/');
                console.log('📝 Extracted file path from URL:', filePath);

                const downloadUrl = await designAnalysisService.getImageUrl(filePath);
                if (downloadUrl) {
                  imageUrl = downloadUrl;
                  console.log('✅ Successfully converted HTTP URL to blob URL');
                } else {
                  throw new Error('Failed to download image from extracted path');
                }
              } else {
                throw new Error('Cannot extract valid file path from URL');
              }
            } catch (urlError) {
              console.warn('⚠️ Failed to process HTTP URL, trying direct load:', urlError);
              // Fall back to using the original URL
            }
          }

          // FIXED: Clean up previous object URLs BEFORE setting new one
          if (previewUrl && previewUrl.startsWith('blob:')) {
            console.log('🧹 Cleaning up previous object URL');
            URL.revokeObjectURL(previewUrl);
          }

          // Test if the image URL is valid before setting it
          console.log('🧪 Testing image URL validity...');
          await new Promise((resolve, reject) => {
            const testImg = new Image();
            testImg.onload = () => {
              console.log('✅ Image URL is valid and loads successfully');
              resolve(true);
            };
            testImg.onerror = (error) => {
              console.error('❌ Image URL failed to load:', error);
              reject(new Error('Image URL failed to load'));
            };
            testImg.src = imageUrl;

            // Timeout after 10 seconds
            setTimeout(() => {
              if (!testImg.complete) {
                console.error('❌ Image load timeout');
                reject(new Error('Image load timeout'));
              }
            }, 10000);
          });

          // Set the preview URL after validation
          console.log('✅ Setting preview URL for analysis:', analysis.id);
          setPreviewUrl(imageUrl);

          // Clear loading state on success
          setIsLoadingImage(false);

          // Create a mock file object for consistency with the upload flow
          try {
            const response = await fetch(imageUrl);
            if (response.ok) {
              const blob = await response.blob();
              const file = new File([blob], analysis.original_filename, {
                type: analysis.file_type || 'image/jpeg'
              });
              setSelectedFile(file);
              console.log('✅ Image file object created for analysis:', analysis.id);
            } else {
              throw new Error(`Failed to fetch image: ${response.status}`);
            }
          } catch (fetchError) {
            console.warn('⚠️ Could not create file object, using mock file:', fetchError);
            // Create a minimal file object for display purposes
            const mockFile = new File([''], analysis.original_filename, {
              type: analysis.file_type || 'image/jpeg'
            });
            setSelectedFile(mockFile);
          }

        } catch (imageError) {
          console.error(`🖼️ Could not load image for analysis ${analysis.id}:`, imageError);

          // ENHANCED: Provide specific error messages and debugging info
          let errorMessage = "No se pudo cargar la imagen original, pero los resultados del análisis están disponibles";
          let errorTitle = "Imagen no disponible";
          let debugInfo = "";

          if (imageError instanceof Error) {
            if (imageError.message.includes('network') || imageError.message.includes('fetch')) {
              errorMessage = "Error de conexión al cargar la imagen. Verifica tu conexión a internet.";
              errorTitle = "Error de conexión";
              debugInfo = "Network/fetch error";
            } else if (imageError.message.includes('404') || imageError.message.includes('not found')) {
              errorMessage = "La imagen original ya no está disponible en el servidor.";
              errorTitle = "Imagen no encontrada";
              debugInfo = "File not found (404)";
            } else if (imageError.message.includes('403') || imageError.message.includes('permission')) {
              errorMessage = "No tienes permisos para acceder a esta imagen.";
              errorTitle = "Acceso denegado";
              debugInfo = "Permission denied (403)";
            } else if (imageError.message.includes('timeout')) {
              errorMessage = "La carga de la imagen tardó demasiado tiempo.";
              errorTitle = "Tiempo de espera agotado";
              debugInfo = "Load timeout";
            } else if (imageError.message.includes('Failed to obtain image URL')) {
              errorMessage = "Error en la configuración de almacenamiento. Contacta al administrador.";
              errorTitle = "Error de configuración";
              debugInfo = "Storage configuration error";
            } else {
              debugInfo = imageError.message;
            }
          }

          // Log detailed error information for debugging
          console.error('🔍 Image loading error details:', {
            analysisId: analysis.id,
            fileName: analysis.original_filename,
            filePath: analysis.file_url,
            errorType: debugInfo,
            errorMessage: imageError.message,
            stack: imageError.stack
          });

          toast({
            title: errorTitle,
            description: errorMessage,
            variant: "destructive",
          });

          // Add agent message about the image issue with helpful context
          const agentMessage = debugInfo.includes('configuration')
            ? `⚠️ ${errorMessage} Esto puede deberse a un problema temporal del servidor.`
            : `⚠️ ${errorMessage} Los resultados del análisis se han cargado correctamente.`;

          setAgentMessages((prev) => [
            ...prev,
            {
              text: agentMessage,
              isUser: false,
              timestamp: new Date(),
            },
          ]);

          // Clear loading state on error
          setIsLoadingImage(false);
        }
      } else {
        console.log('🖼️ No file_url available for this analysis - this is expected for older analyses');

        // Clear any existing preview URL
        if (previewUrl && previewUrl.startsWith('blob:')) {
          URL.revokeObjectURL(previewUrl);
        }
        setPreviewUrl(null);
        setIsLoadingImage(false);

        // Show informative message instead of error
        setAgentMessages((prev) => [
          ...prev,
          {
            text: `📋 He cargado el análisis "${analysis.custom_name || analysis.original_filename}". La imagen original no está disponible porque este análisis fue guardado antes de la implementación del almacenamiento de imágenes.`,
            isUser: false,
            timestamp: new Date(),
          },
        ]);
      }

      // CRITICAL FIX: Switch to results tab AFTER image loading is complete
      // This allows users to immediately see the loaded analysis results and image
      setTimeout(() => {
        console.log('🔄 Switching to results tab for loaded analysis:', analysis.id);
        setActiveTab("theory");
      }, 100);

      // Record the view
      if (user && user.id !== "anonymous") {
        designAnalysisService.recordView(analysis.id).catch(console.error);
      }

      // Success message with specific analysis info
      const successMessage = analysis.file_url
        ? "Los resultados y la imagen original han sido cargados"
        : "Los resultados han sido cargados (imagen no disponible)";

      toast({
        title: "Análisis cargado",
        description: successMessage,
      });

      console.log('✅ Analysis loaded successfully:', {
        analysisId: analysis.id,
        hasImage: !!analysis.file_url,
        filename: analysis.original_filename
      });
    } catch (error) {
      console.error("Error loading analysis:", error);
      toast({
        title: "Error",
        description: "No se pudo cargar el análisis completo",
        variant: "destructive",
      });
    }
  };

  const handleRegenerateAnalysis = async (analysis: DesignAnalysis) => {
    if (!user || user.id === "anonymous") return;

    // Clear current results
    setAnalysisResults(null);
    setAnalysisComplete(false);

    // Switch to analyze tab
    setActiveTab("analyze");

    // Record the regeneration
    try {
      await designAnalysisService.recordView(analysis.id);
    } catch (error) {
      console.error("Error recording regeneration:", error);
    }

    toast({
      title: "Listo para regenerar",
      description: "Sube una nueva imagen para regenerar el análisis",
    });
  };

  // Añadir los mensajes de introducción del agente al cargar
  useEffect(() => {
    const initialMessages = agentIntroMessages.map((text, index) => ({
      text,
      isUser: false,
      timestamp: new Date(Date.now() + index * 800), // Espaciar los mensajes
    }));

    // Programar los mensajes para que aparezcan secuencialmente
    initialMessages.forEach((message, index) => {
      setTimeout(
        () => {
          setAgentMessages((prev) => [...prev, message]);
        },
        1000 + index * 1500,
      );
    });
  }, []);

  // Este efecto ya no se utiliza ya que el progreso se maneja en handleAnalyzeClick
  useEffect(() => {
    if (isAnalyzing) {
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 4;
        });
      }, 150);

      return () => {
        clearInterval(interval);
      };
    }
  }, [isAnalyzing]);

  // Efecto para auto-scroll mensajes a la última posición
  useEffect(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [agentMessages]);

  // Cleanup object URLs on component unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Función para añadir mensajes del agente en cierta etapa
  const addAgentMessages = (
    stage: keyof typeof agentAnalysisMessages,
    delay = 1000,
  ) => {
    const messages = agentAnalysisMessages[stage];

    messages.forEach((text, index) => {
      setTimeout(
        () => {
          setAgentMessages((prev) => [
            ...prev,
            {
              text,
              isUser: false,
              timestamp: new Date(),
            },
          ]);
        },
        delay + index * 1500,
      );
    });
  };

  // Función para determinar mensajes basados en la puntuación
  const addResultMessages = (score: number) => {
    let stage: keyof typeof agentAnalysisMessages;

    if (score >= 85) {
      stage = "excellent";
    } else if (score >= 70) {
      stage = "good";
    } else if (score >= 50) {
      stage = "average";
    } else {
      stage = "poor";
    }

    addAgentMessages(stage, 1000);

    // Añadir recomendaciones específicas después de 4 segundos
    setTimeout(() => {
      const recommendations = analysisResults?.recommendations || [];
      if (recommendations.length > 0) {
        const topRecommendation =
          recommendations.find((r) => r.importance === "alta") ||
          recommendations[0];

        setAgentMessages((prev) => [
          ...prev,
          {
            text: `Una recomendación importante: ${topRecommendation.recommendation}`,
            isUser: false,
            timestamp: new Date(),
          },
        ]);
      }
    }, 4000);
  };



  // Manejar la selección de archivos
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Verificar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description:
            "Por favor, selecciona un archivo de imagen (JPEG, PNG, GIF, etc.)",
          variant: "destructive",
        });
        return;
      }

      setSelectedFile(file);
      setAnalysisComplete(false);
      setAnalysisResults(null);
      setCurrentAnalysisId(null); // Reset saved analysis state for new image
      setIsLoadedFromSaved(false); // Reset loaded from saved state
      setOriginalImageHash(null); // Reset original image hash

      // Añadir mensaje de confirmación del agente
      setAgentMessages((prev) => [
        ...prev,
        {
          text: `¡Gracias! He recibido tu imagen "${file.name}". Cuando estés listo, haz clic en "Analizar Complejidad" para comenzar el análisis.`,
          isUser: false,
          timestamp: new Date(),
        },
      ]);

      // Crear URL para preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Limpiar URL al desmontar
      return () => URL.revokeObjectURL(objectUrl);
    }
  };

  const handleRemoveFile = () => {
    // Clean up object URL if it exists
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }

    setSelectedFile(null);
    setPreviewUrl(null);
    setAnalysisComplete(false);
    setAnalysisResults(null);
    setCurrentAnalysisId(null); // Reset saved analysis state when removing file
    setIsLoadedFromSaved(false); // Reset loaded from saved state
    setOriginalImageHash(null); // Reset original image hash
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Función para generar análisis simulado cuando el backend no está disponible
  const generateFallbackAnalysis = async (file: File): Promise<any> => {
    // Simular análisis basado en características del archivo
    const fileSize = file.size;
    const fileName = file.name.toLowerCase();

    // Análisis básico basado en el tamaño y tipo de archivo
    let baseScore = 75; // Puntuación base

    // Ajustar puntuación basada en el tamaño del archivo
    if (fileSize > 2 * 1024 * 1024) { // > 2MB
      baseScore -= 10; // Archivos grandes suelen ser más complejos
    } else if (fileSize < 100 * 1024) { // < 100KB
      baseScore += 5; // Archivos pequeños suelen ser más simples
    }

    // Ajustar basado en el tipo de archivo
    if (fileName.includes('logo') || fileName.includes('icon')) {
      baseScore += 10; // Logos suelen ser más simples
    } else if (fileName.includes('poster') || fileName.includes('banner')) {
      baseScore -= 5; // Posters suelen ser más complejos
    }

    // Generar variación aleatoria para simular análisis real
    const variation = Math.floor(Math.random() * 20) - 10; // -10 a +10
    const finalScore = Math.max(0, Math.min(100, baseScore + variation));

    // Generar puntuaciones para cada área
    const areas = [
      {
        name: "Jerarquía Visual",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Análisis de la estructura jerárquica y puntos focales del diseño"
      },
      {
        name: "Composición y Balance",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Evaluación del equilibrio visual y distribución de elementos"
      },
      {
        name: "Contraste y Legibilidad",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Análisis de la claridad visual y facilidad de lectura"
      },
      {
        name: "Espacio en Blanco",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Evaluación del uso efectivo del espacio negativo"
      },
      {
        name: "Coherencia Tipográfica",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Análisis de la consistencia y jerarquía tipográfica"
      },
      {
        name: "Armonía Cromática",
        score: Math.max(0, Math.min(10, Math.floor(finalScore / 10) + Math.floor(Math.random() * 3) - 1)),
        description: "Evaluación de la paleta de colores y armonía visual"
      }
    ];

    const recommendations = [
      "🎯 Considera establecer un punto focal más claro para mejorar la jerarquía visual",
      "📐 Evalúa el balance de elementos para optimizar la composición",
      "🔍 Revisa el contraste para asegurar una buena legibilidad",
      "🌬️ Aprovecha mejor el espacio en blanco para dar respiración al diseño",
      "🔤 Mantén consistencia en la tipografía para mayor profesionalismo",
      "🎨 Considera simplificar la paleta de colores para mayor armonía"
    ];

    return {
      score: finalScore,
      complexity: {
        hierarchy: areas[0].score,
        composition: areas[1].score,
        contrast: areas[2].score,
        whitespace: areas[3].score,
        typography: areas[4].score,
        color: areas[5].score
      },
      areas: areas,
      recommendations: recommendations.slice(0, 3), // Mostrar solo 3 recomendaciones
      visuai_insights: [
        "Análisis realizado con algoritmo de VisuAI en modo offline",
        `Archivo analizado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`,
        "Para análisis más detallado, asegúrate de que el backend esté funcionando"
      ],
      analysis_summary: "Análisis completado usando algoritmo local de VisuAI",
      agent_message: `He analizado tu diseño "${file.name}" usando mi algoritmo local. Puntuación: ${finalScore}/100`,
      saved_to_database: false
    };
  };

  const handleAnalyzeClick = async () => {
    if (!selectedFile) {
      toast({
        title: "Archivo requerido",
        description: "Por favor, selecciona una imagen para analizar",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    setProgress(0);

    // Reset analysis state for new analysis
    setCurrentAnalysisId(null);
    setIsLoadedFromSaved(false);
    setOriginalImageHash(null);

    // Añadir mensajes del agente cuando comienza el análisis
    addAgentMessages("start", 200);

    try {
      // Simular progreso mientras se procesa la solicitud
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Mantener en 90% hasta que termine la respuesta
          }
          return prev + 3;
        });
      }, 200);

      let analysisData;
      let usingFallback = false;

      try {
        // Intentar usar el backend primero
        const formData = new FormData();
        formData.append("design", selectedFile);

        // Prepare headers with authentication if user is logged in
        const headers: HeadersInit = {};
        if (isAuthenticated && user) {
          // Get the current session token from Supabase
          const { data: { session } } = await import('@/lib/supabase').then(m => m.supabase.auth.getSession());
          if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`;
          }
        }

        // Enviar solicitud a la API con timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos timeout

        const response = await fetch("/api/analyze-design", {
          method: "POST",
          headers,
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Backend error: ${response.status}`);
        }

        analysisData = await response.json();

      } catch (backendError) {
        console.warn("Backend no disponible, usando análisis local:", backendError);

        // Usar análisis simulado como fallback
        analysisData = await generateFallbackAnalysis(selectedFile);
        usingFallback = true;

        // Añadir mensaje informativo
        setAgentMessages((prev) => [
          ...prev,
          {
            text: "El servidor principal no está disponible, pero he realizado un análisis local de tu diseño. Los resultados son una estimación basada en características del archivo.",
            isUser: false,
            timestamp: new Date(),
          },
        ]);
      }

      // Limpiar intervalo
      clearInterval(progressInterval);

      // Asignar iconos a las áreas (mapeo actualizado para nuevos nombres)
      const areaIcons: Record<string, JSX.Element> = {
        "Jerarquía Visual": <Zap className="h-5 w-5 text-purple-500" />,
        "Composición y Balance": <Layout className="h-5 w-5 text-blue-500" />,
        "Contraste y Legibilidad": <Type className="h-5 w-5 text-emerald-500" />,
        "Espacio en Blanco": <Layers className="h-5 w-5 text-indigo-500" />,
        "Coherencia Tipográfica": <Type className="h-5 w-5 text-amber-500" />,
        "Armonía Cromática": <ImageIcon className="h-5 w-5 text-pink-500" />,
        // Fallback para nombres antiguos
        "Uso del color": <ImageIcon className="h-5 w-5 text-pink-500" />,
        "Estructura y layout": <Layout className="h-5 w-5 text-blue-500" />,
        "Tipografía": <Type className="h-5 w-5 text-emerald-500" />,
        "Cantidad de elementos": <Layers className="h-5 w-5 text-indigo-500" />,
      };

      // Completar resultados con iconos
      const enhancedResults = {
        ...analysisData,
        areas: analysisData.areas.map((area: any) => ({
          ...area,
          icon: areaIcons[area.name] || (
            <Layout className="h-5 w-5 text-blue-500" />
          ),
        })),
      };

      setProgress(100);
      setTimeout(async () => {
        setIsAnalyzing(false);
        setAnalysisComplete(true);
        setAnalysisResults(enhancedResults);

        // Store analysis ID if saved to database
        if (analysisData.analysis_id) {
          setCurrentAnalysisId(analysisData.analysis_id);
        }

        // FIXED: Simplified auto-save logic - always try to save for authenticated users
        if (isAuthenticated && selectedFile && user?.id) {
          try {
            console.log('🔄 Auto-saving analysis to history...', {
              usingFallback,
              alreadySaved: analysisData.saved_to_database,
              userId: user.id,
              userIdType: typeof user.id,
              isAuthenticated,
              selectedFileName: selectedFile.name,
              selectedFileSize: selectedFile.size
            });

            const autoSaveData = {
              user_id: user.id,
              original_filename: selectedFile.name,
              file_size: selectedFile.size,
              file_type: selectedFile.type,
              overall_score: enhancedResults.score,
              complexity_scores: enhancedResults.complexity,
              analysis_areas: enhancedResults.areas.map(area => ({
                name: area.name,
                score: area.score,
                description: area.description,
                recommendations: area.recommendations || []
              })),
              recommendations: enhancedResults.recommendations || [],
              ai_analysis_summary: enhancedResults.analysis_summary,
              gemini_analysis: enhancedResults.gemini_analysis,
              agent_message: enhancedResults.agent_message,
              visuai_insights: enhancedResults.visuai_insights,
              tags: usingFallback ? ['fallback', 'local-analysis'] : []
            };

            // Only save if not already saved by backend
            if (!analysisData.saved_to_database) {
              const savedAnalysis = await saveAnalysis(autoSaveData, selectedFile);
              if (savedAnalysis) {
                setCurrentAnalysisId(savedAnalysis.id);
                setIsLoadedFromSaved(false);

                // Update analysis results to mark as saved
                setAnalysisResults({
                  ...enhancedResults,
                  saved_to_database: true
                });

                // FIXED: Auto-refresh history UI after successful save
                if (refetchAnalyses) {
                  await refetchAnalyses();
                  console.log('🔄 History refreshed after auto-save');
                }

                console.log('✅ Analysis auto-saved successfully:', savedAnalysis.id);
              }
            } else {
              console.log('ℹ️ Analysis already saved by backend, skipping auto-save');
            }

            // Success message
            const messageText = usingFallback
              ? "He completado el análisis local y lo he guardado automáticamente en tu historial. Para análisis más detallados, asegúrate de que el servidor esté funcionando."
              : "¡Perfecto! He completado el análisis y lo he guardado automáticamente en tu historial personal.";

            setAgentMessages((prev) => [
              ...prev,
              {
                text: messageText,
                isUser: false,
                timestamp: new Date(),
              },
            ]);

          } catch (autoSaveError) {
            console.error('Error auto-saving analysis:', autoSaveError);
            const errorText = usingFallback
              ? "He realizado un análisis local. Hubo un problema al guardarlo automáticamente, pero puedes verlo en esta sesión."
              : "¡Análisis completado! Hubo un problema al guardarlo automáticamente, pero puedes verlo en esta sesión.";

            setAgentMessages((prev) => [
              ...prev,
              {
                text: errorText,
                isUser: false,
                timestamp: new Date(),
              },
            ]);
          }
        } else if (!isAuthenticated) {
          setAgentMessages((prev) => [
            ...prev,
            {
              text: "¡Análisis completado! Inicia sesión para guardar automáticamente tus análisis en el historial.",
              isUser: false,
              timestamp: new Date(),
            },
          ]);
        }

        // Añadir mensajes del agente basados en el resultado
        addResultMessages(enhancedResults.score);
      }, 500);
    } catch (error) {
      console.error("Error crítico en el análisis:", error);
      setIsAnalyzing(false);

      // Intentar análisis de fallback como último recurso
      try {
        console.log("Intentando análisis de emergencia...");
        const fallbackData = await generateFallbackAnalysis(selectedFile);

        // Asignar iconos
        const areaIcons: Record<string, JSX.Element> = {
          "Jerarquía Visual": <Zap className="h-5 w-5 text-purple-500" />,
          "Composición y Balance": <Layout className="h-5 w-5 text-blue-500" />,
          "Contraste y Legibilidad": <Type className="h-5 w-5 text-emerald-500" />,
          "Espacio en Blanco": <Layers className="h-5 w-5 text-indigo-500" />,
          "Coherencia Tipográfica": <Type className="h-5 w-5 text-amber-500" />,
          "Armonía Cromática": <ImageIcon className="h-5 w-5 text-pink-500" />,
        };

        const enhancedResults = {
          ...fallbackData,
          areas: fallbackData.areas.map((area: any) => ({
            ...area,
            icon: areaIcons[area.name] || <Layout className="h-5 w-5 text-blue-500" />,
          })),
        };

        setProgress(100);
        setTimeout(async () => {
          setIsAnalyzing(false);
          setAnalysisComplete(true);
          setAnalysisResults(enhancedResults);

          // FIXED: Emergency fallback auto-save logic
          if (isAuthenticated && selectedFile && user?.id) {
            try {
              console.log('🔄 Auto-saving emergency fallback analysis to history...', {
                userId: user.id,
                userIdType: typeof user.id,
                isAuthenticated,
                selectedFileName: selectedFile.name
              });

              const autoSaveData = {
                user_id: user.id,
                original_filename: selectedFile.name,
                file_size: selectedFile.size,
                file_type: selectedFile.type,
                overall_score: enhancedResults.score,
                complexity_scores: enhancedResults.complexity,
                analysis_areas: enhancedResults.areas.map(area => ({
                  name: area.name,
                  score: area.score,
                  description: area.description,
                  recommendations: area.recommendations || []
                })),
                recommendations: enhancedResults.recommendations || [],
                ai_analysis_summary: enhancedResults.analysis_summary,
                gemini_analysis: enhancedResults.gemini_analysis,
                agent_message: enhancedResults.agent_message,
                visuai_insights: enhancedResults.visuai_insights,
                tags: ['emergency-fallback', 'local-analysis']
              };

              const savedAnalysis = await saveAnalysis(autoSaveData, selectedFile);
              if (savedAnalysis) {
                setCurrentAnalysisId(savedAnalysis.id);
                setIsLoadedFromSaved(false);

                // Update analysis results to mark as saved
                setAnalysisResults({
                  ...enhancedResults,
                  saved_to_database: true
                });

                // FIXED: Auto-refresh history UI after successful emergency save
                if (refetchAnalyses) {
                  await refetchAnalyses();
                  console.log('🔄 History refreshed after emergency auto-save');
                }

                setAgentMessages((prev) => [
                  ...prev,
                  {
                    text: "He realizado un análisis de emergencia y lo he guardado automáticamente en tu historial. Los resultados son una estimación básica del diseño.",
                    isUser: false,
                    timestamp: new Date(),
                  },
                ]);
              }
            } catch (autoSaveError) {
              console.error('Error auto-saving emergency fallback analysis:', autoSaveError);
              setAgentMessages((prev) => [
                ...prev,
                {
                  text: "He realizado un análisis de emergencia. Hubo un problema al guardarlo automáticamente, pero puedes verlo en esta sesión.",
                  isUser: false,
                  timestamp: new Date(),
                },
              ]);
            }
          } else {
            setAgentMessages((prev) => [
              ...prev,
              {
                text: isAuthenticated
                  ? "He realizado un análisis de emergencia usando mi algoritmo local. Los resultados son una estimación básica del diseño."
                  : "He realizado un análisis de emergencia. Inicia sesión para guardar automáticamente tus análisis en el historial.",
                isUser: false,
                timestamp: new Date(),
              },
            ]);
          }

          addResultMessages(enhancedResults.score);
        }, 500);

      } catch (fallbackError) {
        console.error("Error en análisis de fallback:", fallbackError);

        // Añadir mensaje de error del agente
        setAgentMessages((prev) => [
          ...prev,
          {
            text: "Lo siento, tuve un problema al analizar esta imagen. ¿Podrías intentar con otra imagen o verificar el formato?",
            isUser: false,
            timestamp: new Date(),
          },
        ]);

        toast({
          title: "Error en el análisis",
          description: "No se pudo analizar el diseño. Intenta con otra imagen o verifica tu conexión.",
          variant: "destructive",
        });
      }
    }
  };

  // Renderizar el componente de clasificación visual
  const renderComplexityGauge = (
    label: string,
    value: number,
    max: number = 10,
  ) => {
    // Determinar el color basado en el valor
    let color = "text-green-500";
    if (value > max * 0.7) color = "text-red-500";
    else if (value > max * 0.4) color = "text-amber-500";

    return (
      <div className="flex flex-col items-center">
        <span className="text-sm text-gray-600 mb-1">{label}</span>
        <div className="relative w-16 h-16 flex items-center justify-center">
          <svg className="w-full h-full" viewBox="0 0 36 36">
            <path
              className="text-gray-200"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeWidth="3"
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            <path
              className={color}
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeWidth="3"
              strokeDasharray={`${value * 10}, 100`}
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            <text
              x="18"
              y="20.5"
              className="text-2xl font-medium"
              textAnchor="middle"
            >
              {value}
            </text>
          </svg>
        </div>
      </div>
    );
  };

  // Renderizar el medidor de puntuación principal
  const renderScoreGauge = (score: number) => {
    // Determinar el color y mensaje basado en el puntaje
    let color, message;
    if (score >= 85) {
      color = "text-green-500";
      message = "Excelente";
    } else if (score >= 70) {
      color = "text-blue-500";
      message = "Bueno";
    } else if (score >= 50) {
      color = "text-amber-500";
      message = "Mejorable";
    } else {
      color = "text-red-500";
      message = "Problemático";
    }

    return (
      <div className="flex flex-col items-center justify-center py-8">
        <div className="relative w-48 h-48 flex items-center justify-center mb-4">
          <svg className="w-full h-full" viewBox="0 0 36 36">
            {/* Círculo de fondo */}
            <path
              fill="none"
              stroke="#e5e7eb"
              strokeLinecap="round"
              strokeWidth="3"
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            {/* Círculo de progreso */}
            <path
              fill="none"
              stroke={
                score >= 85 ? "#10b981" :
                score >= 70 ? "#3b82f6" :
                score >= 50 ? "#f59e0b" : "#ef4444"
              }
              strokeLinecap="round"
              strokeWidth="3"
              strokeDasharray={`${score}, 100`}
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            {/* Número principal */}
            <text
              x="18"
              y="16"
              textAnchor="middle"
              dominantBaseline="middle"
              style={{
                fontSize: '8px',
                fontWeight: 'bold',
                fill: '#1f2937'
              }}
            >
              {score}
            </text>
            {/* Texto /100 */}
            <text
              x="18"
              y="22"
              textAnchor="middle"
              dominantBaseline="middle"
              style={{
                fontSize: '3px',
                fill: '#6b7280'
              }}
            >
              /100
            </text>
          </svg>
        </div>
        <Badge
          className={`text-white text-sm py-1 px-3 ${
            color === "text-green-500"
              ? "bg-green-500"
              : color === "text-blue-500"
                ? "bg-blue-500"
                : color === "text-amber-500"
                  ? "bg-amber-500"
                  : "bg-red-500"
          }`}
        >
          {message}
        </Badge>
      </div>
    );
  };

  // Renderizar componente de área con puntuación
  const renderAreaScore = (area: AnalysisResult["areas"][0], index: number) => {
    // Determinar el color basado en la puntuación
    let color = "bg-red-100 text-red-800 border-red-200";
    if (area.score >= 85) {
      color = "bg-green-100 text-green-800 border-green-200";
    } else if (area.score >= 70) {
      color = "bg-blue-100 text-blue-800 border-blue-200";
    } else if (area.score >= 50) {
      color = "bg-amber-100 text-amber-800 border-amber-200";
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 * index }}
        className={`p-4 rounded-lg border ${color} mb-3`}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            {area.icon}
            <h4 className="font-medium ml-2">{area.name}</h4>
          </div>
          <Badge variant="outline" className={color}>
            {area.score} / 100
          </Badge>
        </div>
        <p className="text-sm mb-3">{area.description}</p>
        <div className="mt-2">
          <p className="text-sm font-medium mb-1">Descripción:</p>
          <p className="text-sm text-gray-600">{area.description}</p>
        </div>
      </motion.div>
    );
  };

  // Renderizar burbuja de chat
  const renderMessage = (message: AgentMessage, index: number) => {
    return (
      <motion.div
        key={index}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`flex ${message.isUser ? "justify-end" : "justify-start"} mb-3`}
      >
        {!message.isUser && (
          <div className="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden mr-2 bg-indigo-100">
            <img
              src={aiCharacter}
              alt="AI Agent"
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <div
          className={`rounded-lg px-4 py-2 max-w-[80%] ${
            message.isUser
              ? "bg-blue-600 text-white rounded-br-none"
              : "bg-gray-100 text-gray-800 rounded-bl-none"
          }`}
        >
          <p className="text-sm">{message.text}</p>
          <p className="text-xs opacity-70 mt-1">
            {message.timestamp.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      {/* Asistente IA expandido */}
      {isAgentMinimized ? (
        <div className="fixed z-10 bottom-4 right-4 transition-all duration-300">
          <motion.button
            onClick={() => setIsAgentMinimized(false)}
            className="h-14 w-14 rounded-full bg-blue-600 flex items-center justify-center shadow-lg hover:bg-blue-700 border-2 border-white"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <img
                src={aiCharacter}
                alt="VisuAI"
                className="w-full h-full object-cover"
              />
            </div>
          </motion.button>
        </div>
      ) : (
        <div
          className={`fixed z-10 bottom-4 right-4 max-w-md w-full md:w-96 transition-all duration-300`}
        >
          <Card className="border-2 border-blue-200 shadow-lg">
            <CardHeader className="bg-blue-600 text-white p-3 pb-2 flex flex-row justify-between items-center">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-white mr-2">
                  <img
                    src={aiCharacter}
                    alt="VisuAI"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardTitle className="text-base">VisuAI Asistente</CardTitle>
              </div>
              <button
                onClick={() => setIsAgentMinimized(true)}
                className="h-6 w-6 rounded-full bg-blue-700 flex items-center justify-center hover:bg-blue-800"
                title="Minimizar"
              >
                <Minus className="h-3 w-3" />
              </button>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="h-[240px] overflow-y-auto p-2"
                style={{ scrollBehavior: "smooth" }}
              >
                {agentMessages.length === 0 ? (
                  <div className="h-full flex items-center justify-center text-gray-400 text-sm">
                    <p>Cargando asistente visual...</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {agentMessages.map((message, i) =>
                      renderMessage(message, i),
                    )}
                    <div ref={messageEndRef}></div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analyze">Análisis de Complejidad</TabsTrigger>
          <TabsTrigger value="theory" disabled={!analysisResults}>
            Resultados Detallados
          </TabsTrigger>
          <TabsTrigger value="history" disabled={!isAuthenticated && process.env.NODE_ENV !== 'development'} className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Historial ({recentAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="favorites" disabled={!isAuthenticated && process.env.NODE_ENV !== 'development'} className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Favoritos ({favoriteAnalyses.length})
          </TabsTrigger>

        </TabsList>

        <TabsContent value="analyze" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Panel de Carga de Imágenes */}
            <Card className="border-2 border-gray-200 shadow-sm">
              <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                <CardTitle className="text-blue-600 flex items-center gap-2">
                  <Upload className="h-5 w-5 text-blue-600" />
                  Subir Diseño
                </CardTitle>
                <CardDescription>
                  Sube un diseño para analizar su complejidad visual
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                {!previewUrl ? (
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                    <h3 className="font-medium text-gray-700 mb-1">
                      Arrastra y suelta tu diseño
                    </h3>
                    <p className="text-gray-500 text-sm mb-4">
                      O haz clic para seleccionar
                    </p>
                    <p className="text-xs text-gray-400">
                      PNG, JPG o GIF hasta 5MB
                    </p>
                    <input
                      type="file"
                      className="hidden"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      accept="image/*"
                    />
                  </div>
                ) : (
                  <div className="relative">
                    <div className="relative rounded-lg overflow-hidden border border-gray-200">
                      <img
                        src={previewUrl}
                        alt="Design preview"
                        className="w-full h-auto object-contain max-h-[300px]"
                      />
                      <button
                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                        onClick={handleRemoveFile}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="mt-4">
                      <p className="text-sm font-medium mb-1">
                        Archivo seleccionado:
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedFile?.name}
                      </p>
                    </div>
                    <Button
                      className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={handleAnalyzeClick}
                      disabled={isAnalyzing}
                    >
                      {isAnalyzing ? "Analizando..." : "Analizar Complejidad"}
                      {!isAnalyzing && <ArrowRight className="ml-2 h-4 w-4" />}
                    </Button>

                    {isAnalyzing && (
                      <div className="mt-4">
                        <p className="text-sm text-center mb-2">
                          Analizando diseño...
                        </p>
                        <Progress value={progress} className="h-2" />
                        <div className="grid grid-cols-2 text-xs text-gray-500 mt-2">
                          <div>Detectando elementos...</div>
                          <div className="text-right">{progress}%</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Panel de Resultados */}
            <Card className="border-2 border-gray-200 shadow-sm">
              <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                <CardTitle className="text-blue-600 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Resultados del Análisis
                </CardTitle>
                <CardDescription>
                  Puntuación y análisis de complejidad visual de tu diseño
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {!analysisComplete ? (
                  <div className="text-center py-16 text-gray-500">
                    <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="font-medium text-gray-700 mb-1">
                      Aún no hay resultados
                    </h3>
                    <p className="text-sm">
                      {previewUrl
                        ? "Haz clic en 'Analizar Complejidad' para iniciar el análisis"
                        : "Selecciona una imagen para iniciar el análisis"}
                    </p>
                  </div>
                ) : (
                  <AnimatePresence>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      {analysisResults && (
                        <>
                          <div className="text-center mb-6">
                            <h3 className="text-lg font-medium text-gray-800 mb-2">
                              Puntuación de Diseño
                            </h3>
                            {renderScoreGauge(analysisResults.score)}

                            {/* Analysis automatically saved to history */}
                            <div className="flex justify-center gap-2 mt-4">
                              {isAuthenticated ? (
                                <Badge variant="secondary" className="text-blue-600 border-blue-200">
                                  <History className="h-3 w-3 mr-1" />
                                  Guardado automáticamente en historial
                                </Badge>
                              ) : (
                                <div className="text-center">
                                  <p className="text-sm text-gray-600 mb-2">
                                    Inicia sesión para guardar tus análisis automáticamente
                                  </p>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      console.log('🔐 Login button clicked');
                                      window.location.href = '/login';
                                    }}
                                  >
                                    Iniciar Sesión
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="mb-4">
                            <h4 className="font-medium text-gray-700 mb-3">
                              Métricas de complejidad
                            </h4>
                            <div className="grid grid-cols-3 gap-3 mb-4">
                              {renderComplexityGauge(
                                "Jerarquía",
                                analysisResults.complexity.hierarchy || analysisResults.complexity.layout || 5,
                              )}
                              {renderComplexityGauge(
                                "Composición",
                                analysisResults.complexity.composition || analysisResults.complexity.layout || 5,
                              )}
                              {renderComplexityGauge(
                                "Contraste",
                                analysisResults.complexity.contrast || analysisResults.complexity.color || 5,
                              )}
                            </div>
                            <div className="grid grid-cols-3 gap-3">
                              {renderComplexityGauge(
                                "Espacio",
                                analysisResults.complexity.whitespace || 5,
                              )}
                              {renderComplexityGauge(
                                "Tipografía",
                                analysisResults.complexity.typography || 5,
                              )}
                              {renderComplexityGauge(
                                "Color",
                                analysisResults.complexity.color || 5,
                              )}
                            </div>
                          </div>

                          <div className="flex justify-between items-center border-t border-gray-200 pt-4 mt-4">
                            <div className="flex items-center text-sm text-gray-600">
                              <Info className="h-4 w-4 mr-1" />
                              <span>
                                Mayor puntuación = Diseño más eficiente
                              </span>
                            </div>
                            <Button
                              variant="outline"
                              className="text-blue-600 border-blue-200"
                              onClick={() => setActiveTab("theory")}
                            >
                              Ver detalles
                            </Button>
                          </div>
                        </>
                      )}
                    </motion.div>
                  </AnimatePresence>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Consejos sobre complejidad visual */}
          <Card className="border border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-700 flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                ¿Por qué importa la complejidad visual?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-blue-700">
                La complejidad visual de un diseño afecta directamente la
                experiencia del usuario y las tasas de conversión. Un diseño
                demasiado complejo puede:
              </p>
              <ul className="space-y-2 text-blue-700">
                <li className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                  <span>
                    Aumentar la carga cognitiva del usuario, dificultando la
                    comprensión
                  </span>
                </li>
                <li className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                  <span>
                    Reducir las tasas de conversión en hasta un 20% según
                    estudios
                  </span>
                </li>
                <li className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                  <span>
                    Distraer del mensaje principal y llamados a la acción
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                  <span>
                    Un diseño bien balanceado mejora el engagement y la
                    retención
                  </span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="theory" className="space-y-6">
          {analysisResults && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1">
                <Card className="border-2 border-gray-200 shadow-sm h-full">
                  <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                    <CardTitle className="text-blue-600 flex items-center gap-2">
                      <ImageIcon className="h-5 w-5 text-blue-600" />
                      Diseño Analizado
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6">
                    {previewUrl ? (
                      <div className="rounded-lg overflow-hidden border border-gray-200">
                        <img
                          src={previewUrl}
                          alt="Design preview"
                          className="w-full h-auto object-contain"
                        />
                      </div>
                    ) : (
                      <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center bg-gray-50">
                        <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                        <h3 className="text-sm font-medium text-gray-700 mb-2">
                          Imagen no disponible
                        </h3>
                        <p className="text-xs text-gray-500 mb-4">
                          {isLoadedFromSaved
                            ? "Este análisis fue guardado sin la imagen original"
                            : "No se ha cargado ninguna imagen para analizar"
                          }
                        </p>
                        {isLoadedFromSaved && (
                          <p className="text-xs text-blue-600">
                            💡 Los nuevos análisis incluirán la imagen original
                          </p>
                        )}
                      </div>
                    )}

                    <div className="mt-6">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium">Puntuación global</h3>
                        <Badge className="bg-blue-500">
                          {analysisResults.score}/100
                        </Badge>
                      </div>
                      <Progress
                        value={analysisResults.score}
                        className="h-2 mb-4"
                      />

                      <div className="space-y-3 mt-6">
                        <h4 className="font-medium text-gray-700 mb-2">
                          Complejidad por áreas
                        </h4>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Jerarquía Visual</span>
                          <Badge
                            variant="outline"
                            className="text-blue-600 border-blue-200"
                          >
                            {analysisResults.complexity.hierarchy || analysisResults.complexity.layout || 5}/10
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Composición</span>
                          <Badge
                            variant="outline"
                            className="text-indigo-600 border-indigo-200"
                          >
                            {analysisResults.complexity.composition || analysisResults.complexity.layout || 5}/10
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Contraste</span>
                          <Badge
                            variant="outline"
                            className="text-emerald-600 border-emerald-200"
                          >
                            {analysisResults.complexity.contrast || analysisResults.complexity.color || 5}/10
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Espacio en Blanco</span>
                          <Badge
                            variant="outline"
                            className="text-purple-600 border-purple-200"
                          >
                            {analysisResults.complexity.whitespace || 5}/10
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Tipografía</span>
                          <Badge
                            variant="outline"
                            className="text-orange-600 border-orange-200"
                          >
                            {analysisResults.complexity.typography || 5}/10
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Color</span>
                          <Badge
                            variant="outline"
                            className="text-teal-600 border-teal-200"
                          >
                            {analysisResults.complexity.color || 5}/10
                          </Badge>
                        </div>

                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="md:col-span-2">
                <Card className="border-2 border-gray-200 shadow-sm">
                  <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                    <CardTitle className="text-blue-600 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-blue-600" />
                      Áreas de Mejora
                    </CardTitle>
                    <CardDescription>
                      Análisis detallado y recomendaciones para optimizar tu
                      diseño
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="space-y-6">
                      <div className="space-y-4">
                        {analysisResults.areas.map((area, index) => (
                          <div key={`area-${area.name}-${index}`}>
                            {renderAreaScore(area, index)}
                          </div>
                        ))}
                      </div>

                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                        <div className="flex items-start mb-2">
                          <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
                          <div>
                            <h4 className="font-medium text-amber-800">
                              Recomendaciones principales
                            </h4>
                            <p className="text-sm text-amber-700">
                              Elementos que requieren más atención
                            </p>
                          </div>
                        </div>
                        <ul className="space-y-2">
                          {analysisResults.recommendations
                            .slice(0, 3)
                            .map((rec, index) => (
                              <motion.li
                                key={`recommendation-${index}-${typeof rec === 'string' ? rec.slice(0, 20) : rec.category}`}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{
                                  duration: 0.3,
                                  delay: 0.1 * index,
                                }}
                                className="flex items-start text-amber-800 text-sm"
                              >
                                <Zap className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-amber-600" />
                                <span>{typeof rec === 'string' ? rec : rec.recommendation}</span>
                              </motion.li>
                            ))}
                        </ul>
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <div className="flex gap-2">
                            {isAuthenticated && (
                              <Badge variant="secondary" className="text-blue-600 border-blue-200">
                                <History className="h-3 w-3 mr-1" />
                                Guardado automáticamente
                              </Badge>
                            )}
                          </div>

                          <Button
                            className="bg-blue-600 hover:bg-blue-700"
                            onClick={() => {
                              // Resetear todo el estado
                              setSelectedFile(null);
                              setPreviewUrl(null);
                              setAnalysisComplete(false);
                              setAnalysisResults(null);
                              setIsAnalyzing(false);
                              setProgress(0);
                              setActiveTab("analyze");
                              setCurrentAnalysisId(null);
                              setIsLoadedFromSaved(false); // Reset loaded from saved state
                              setOriginalImageHash(null); // Reset original image hash

                              // Limpiar y abrir el selector de archivos
                              if (fileInputRef.current) {
                                fileInputRef.current.value = "";
                                fileInputRef.current.click();
                              }

                              // Mensaje del agente
                              setAgentMessages((prev) => [
                                ...prev,
                                {
                                  text: "¡Perfecto! Estoy listo para analizar tu nuevo diseño. Selecciona otra imagen para comenzar.",
                                  isUser: false,
                                  timestamp: new Date(),
                                },
                              ]);
                            }}
                          >
                            Analizar otro diseño
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </TabsContent>



        {/* Pestaña de Historial */}
        <TabsContent value="history" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Historial de Análisis
              </h2>
              <Badge variant="secondary">
                {recentAnalyses.length} análisis
              </Badge>
            </div>

            {/* User Notification About Automatic Saving and History Limits */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <History className="h-5 w-5 text-blue-600 mt-0.5" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-900 mb-1">
                    Guardado automático y historial
                  </h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>• <strong>Todos los análisis se guardan automáticamente</strong> en tu historial</p>
                    <p>• El historial muestra los últimos <strong>10 análisis</strong> realizados</p>
                    <p>• Los análisis más antiguos se eliminan automáticamente</p>
                    <p>• Los análisis <strong>marcados como favoritos</strong> se guardan indefinidamente</p>
                    <p>• Usa el botón ❤️ para guardar análisis importantes permanentemente</p>
                  </div>
                </div>
              </div>
            </div>

            {hookIsLoadingAnalyses ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Cargando análisis...</p>
              </div>
            ) : recentAnalyses.length === 0 ? (
              <Card className="p-8 text-center">
                <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="font-medium text-gray-700 mb-2">
                  No hay análisis en tu historial
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Los análisis que realices aparecerán aquí
                </p>
                <Button onClick={() => setActiveTab("analyze")}>
                  Realizar primer análisis
                </Button>
              </Card>
            ) : (
              <div className="grid gap-4">
                {recentAnalyses.map((analysis) => (
                  <AnalysisCard
                    key={analysis.id}
                    analysis={analysis}
                    onLoad={() => handleLoadAnalysis(analysis)}
                    onToggleFavorite={() => handleToggleFavorite(analysis.id)}
                    onRename={(newName) => handleRenameAnalysis(analysis.id, newName)}
                    onDelete={() => handleDeleteAnalysis(analysis.id)}
                    onRegenerate={() => handleRegenerateAnalysis(analysis)}
                    isRenaming={isRenaming === analysis.id}
                    onStartRename={() => {
                      setIsRenaming(analysis.id);
                      setRenameValue(analysis.custom_name || `Análisis ${new Date(analysis.created_at).toLocaleDateString()}`);
                    }}
                    onCancelRename={() => {
                      setIsRenaming(null);
                      setRenameValue("");
                    }}
                    renameValue={renameValue}
                    onRenameValueChange={setRenameValue}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Pestaña de Favoritos */}
        <TabsContent value="favorites" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Análisis Favoritos
              </h2>
              <Badge variant="secondary">
                {favoriteAnalyses.length} favoritos
              </Badge>
            </div>

            {/* User Notification About Favorites */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <Heart className="h-5 w-5 text-amber-600 mt-0.5" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-amber-900 mb-1">
                    Análisis favoritos
                  </h4>
                  <div className="text-sm text-amber-800 space-y-1">
                    <p>• Todos los análisis se guardan automáticamente en el historial</p>
                    <p>• Los análisis favoritos se <strong>guardan permanentemente</strong></p>
                    <p>• No se eliminan automáticamente como el historial regular</p>
                    <p>• Ideal para guardar análisis importantes o de referencia</p>
                    <p>• Haz clic en ❤️ en cualquier análisis para marcarlo como favorito</p>
                  </div>
                </div>
              </div>
            </div>

            {hookIsLoadingAnalyses ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Cargando favoritos...</p>
              </div>
            ) : favoriteAnalyses.length === 0 ? (
              <Card className="p-8 text-center">
                <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="font-medium text-gray-700 mb-2">
                  No tienes análisis favoritos
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Marca análisis como favoritos para acceder rápidamente a ellos
                </p>
                <Button onClick={() => setActiveTab("history")}>
                  Ver historial
                </Button>
              </Card>
            ) : (
              <div className="grid gap-4">
                {favoriteAnalyses.map((analysis) => (
                  <AnalysisCard
                    key={analysis.id}
                    analysis={analysis}
                    onLoad={() => handleLoadAnalysis(analysis)}
                    onToggleFavorite={() => handleToggleFavorite(analysis.id)}
                    onRename={(newName) => handleRenameAnalysis(analysis.id, newName)}
                    onDelete={() => handleDeleteAnalysis(analysis.id)}
                    onRegenerate={() => handleRegenerateAnalysis(analysis)}
                    isRenaming={isRenaming === analysis.id}
                    onStartRename={() => {
                      setIsRenaming(analysis.id);
                      setRenameValue(analysis.custom_name || `Análisis ${new Date(analysis.created_at).toLocaleDateString()}`);
                    }}
                    onCancelRename={() => {
                      setIsRenaming(null);
                      setRenameValue("");
                    }}
                    renameValue={renameValue}
                    onRenameValueChange={setRenameValue}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

      </Tabs>
    </div>
  );
}
